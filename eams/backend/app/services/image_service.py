import os
import uuid
import shutil
from typing import List, Optional
from fastapi import UploadFile, HTTPException
from PIL import Image
import io
from app.core.config import settings

class ImageService:
    def __init__(self):
        self.upload_dir = os.path.join(settings.UPLOAD_DIR, "assets")
        self.thumbnail_dir = os.path.join(self.upload_dir, "thumbnails")
        self.allowed_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
        self.max_file_size = 10 * 1024 * 1024  # 10MB
        self.thumbnail_size = (300, 300)
        
        # Create directories if they don't exist
        os.makedirs(self.upload_dir, exist_ok=True)
        os.makedirs(self.thumbnail_dir, exist_ok=True)
    
    def _validate_image(self, file: UploadFile) -> None:
        """Validate uploaded image file"""
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")
        
        # Check file extension
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in self.allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid file type. Allowed types: {', '.join(self.allowed_extensions)}"
            )
        
        # Check file size
        if file.size and file.size > self.max_file_size:
            raise HTTPException(
                status_code=400, 
                detail=f"File too large. Maximum size: {self.max_file_size // (1024*1024)}MB"
            )
    
    def _generate_filename(self, original_filename: str, asset_id: str) -> str:
        """Generate unique filename for the uploaded image"""
        file_ext = os.path.splitext(original_filename)[1].lower()
        unique_id = str(uuid.uuid4())
        return f"{asset_id}_{unique_id}{file_ext}"
    
    def _create_thumbnail(self, image_path: str, thumbnail_path: str) -> None:
        """Create thumbnail from the original image"""
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary (for PNG with transparency)
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # Create thumbnail
                img.thumbnail(self.thumbnail_size, Image.Resampling.LANCZOS)
                img.save(thumbnail_path, 'JPEG', quality=85)
        except Exception as e:
            print(f"Error creating thumbnail: {e}")
            # If thumbnail creation fails, copy original as fallback
            shutil.copy2(image_path, thumbnail_path)
    
    async def upload_image(self, file: UploadFile, asset_id: str) -> dict:
        """Upload a single image for an asset"""
        self._validate_image(file)
        
        try:
            # Generate unique filename
            filename = self._generate_filename(file.filename, asset_id)
            file_path = os.path.join(self.upload_dir, filename)
            thumbnail_filename = f"thumb_{filename}"
            thumbnail_path = os.path.join(self.thumbnail_dir, thumbnail_filename)
            
            # Save the uploaded file
            with open(file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)
            
            # Create thumbnail
            self._create_thumbnail(file_path, thumbnail_path)
            
            # Generate URLs
            image_url = f"/api/v1/files/assets/{filename}"
            thumbnail_url = f"/api/v1/files/assets/thumbnails/{thumbnail_filename}"
            
            return {
                "filename": filename,
                "original_filename": file.filename,
                "image_url": image_url,
                "thumbnail_url": thumbnail_url,
                "file_size": len(content),
                "content_type": file.content_type
            }
            
        except Exception as e:
            # Clean up any partially created files
            for path in [file_path, thumbnail_path]:
                if 'path' in locals() and os.path.exists(path):
                    os.remove(path)
            raise HTTPException(status_code=500, detail=f"Failed to upload image: {str(e)}")
    
    async def upload_multiple_images(self, files: List[UploadFile], asset_id: str) -> List[dict]:
        """Upload multiple images for an asset"""
        if len(files) > 10:  # Limit to 10 images per asset
            raise HTTPException(status_code=400, detail="Maximum 10 images allowed per asset")
        
        uploaded_images = []
        failed_uploads = []
        
        for file in files:
            try:
                result = await self.upload_image(file, asset_id)
                uploaded_images.append(result)
            except HTTPException as e:
                failed_uploads.append({
                    "filename": file.filename,
                    "error": e.detail
                })
            except Exception as e:
                failed_uploads.append({
                    "filename": file.filename,
                    "error": str(e)
                })
        
        return {
            "uploaded": uploaded_images,
            "failed": failed_uploads,
            "total_uploaded": len(uploaded_images),
            "total_failed": len(failed_uploads)
        }
    
    def delete_image(self, filename: str) -> bool:
        """Delete an image and its thumbnail"""
        try:
            # Delete main image
            image_path = os.path.join(self.upload_dir, filename)
            if os.path.exists(image_path):
                os.remove(image_path)
            
            # Delete thumbnail
            thumbnail_filename = f"thumb_{filename}"
            thumbnail_path = os.path.join(self.thumbnail_dir, thumbnail_filename)
            if os.path.exists(thumbnail_path):
                os.remove(thumbnail_path)
            
            return True
        except Exception as e:
            print(f"Error deleting image {filename}: {e}")
            return False
    
    def delete_asset_images(self, asset_id: str) -> int:
        """Delete all images for a specific asset"""
        deleted_count = 0
        
        try:
            # Find all files that start with asset_id
            for filename in os.listdir(self.upload_dir):
                if filename.startswith(f"{asset_id}_"):
                    if self.delete_image(filename):
                        deleted_count += 1
        except Exception as e:
            print(f"Error deleting images for asset {asset_id}: {e}")
        
        return deleted_count
    
    def get_image_info(self, filename: str) -> Optional[dict]:
        """Get information about an uploaded image"""
        image_path = os.path.join(self.upload_dir, filename)
        thumbnail_filename = f"thumb_{filename}"
        thumbnail_path = os.path.join(self.thumbnail_dir, thumbnail_filename)
        
        if not os.path.exists(image_path):
            return None
        
        try:
            stat = os.stat(image_path)
            return {
                "filename": filename,
                "image_url": f"/api/v1/files/assets/{filename}",
                "thumbnail_url": f"/api/v1/files/assets/thumbnails/{thumbnail_filename}",
                "file_size": stat.st_size,
                "created_at": stat.st_ctime,
                "exists": True,
                "thumbnail_exists": os.path.exists(thumbnail_path)
            }
        except Exception as e:
            print(f"Error getting image info for {filename}: {e}")
            return None

# Global instance
image_service = ImageService()
