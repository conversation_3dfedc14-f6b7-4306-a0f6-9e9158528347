from motor.motor_asyncio import AsyncIOMotorClient
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

class Database:
    client: AsyncIOMotorClient = None
    database = None

db = Database()

async def get_database():
    return db.database

async def connect_to_mongo():
    """Create database connection"""
    try:
        db.client = AsyncIOMotorClient(settings.MONGODB_URL)
        db.database = db.client[settings.DATABASE_NAME]
        
        # Test the connection
        await db.client.admin.command('ping')
        logger.info("Successfully connected to MongoDB")
        
        # Create indexes
        await create_indexes()
        
    except Exception as e:
        logger.error(f"Error connecting to MongoDB: {e}")
        raise

async def close_mongo_connection():
    """Close database connection"""
    if db.client:
        db.client.close()
        logger.info("Disconnected from MongoDB")

async def create_indexes():
    """Create database indexes for better performance"""
    try:
        # Assets collection indexes
        await db.database.assets.create_index("asset_id", unique=True)
        await db.database.assets.create_index("barcode", unique=True, sparse=True)
        await db.database.assets.create_index("location")
        await db.database.assets.create_index("custodian")
        await db.database.assets.create_index("depreciation_class")
        
        # Users collection indexes
        await db.database.users.create_index("email", unique=True)
        await db.database.users.create_index("username", unique=True)
        
        # Verification events indexes
        await db.database.verification_events.create_index("asset_id")
        await db.database.verification_events.create_index("verification_date")
        await db.database.verification_events.create_index("verifier_id")
        
        # Audit trail indexes
        await db.database.audit_trail.create_index("asset_id")
        await db.database.audit_trail.create_index("user_id")
        await db.database.audit_trail.create_index("timestamp")
        await db.database.audit_trail.create_index("action")
        
        # WIP tracking indexes
        await db.database.wip_tracking.create_index("asset_id")
        await db.database.wip_tracking.create_index("project_status")
        
        logger.info("Database indexes created successfully")
        
    except Exception as e:
        logger.error(f"Error creating indexes: {e}")
        raise
