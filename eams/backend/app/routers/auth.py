from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status, Request
from fastapi.security import <PERSON>Auth2PasswordBearer, OAuth2PasswordRequestForm
from datetime import datetime, timedelta
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
from app.models.user import User, UserCreate, UserInDB, Token, TokenData, PasswordChange
from bson import ObjectId
from app.core.config import settings
from app.core.database import get_database
from app.services.audit_service import log_audit_event
from app.models.audit import AuditAction
import re

router = APIRouter()

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/auth/token")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def validate_password_policy(password: str) -> bool:
    """Validate password against ISO 27001 policy"""
    if len(password) < settings.MIN_PASSWORD_LENGTH:
        return False
    
    if settings.REQUIRE_UPPERCASE and not re.search(r'[A-Z]', password):
        return False
    
    if settings.REQUIRE_LOWERCASE and not re.search(r'[a-z]', password):
        return False
    
    if settings.REQUIRE_NUMBERS and not re.search(r'\d', password):
        return False
    
    if settings.REQUIRE_SPECIAL_CHARS and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        return False
    
    return True

def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

async def get_user_by_username(db, username: str):
    user = await db.users.find_one({"username": username})
    if user:
        # Convert ObjectId to string
        user["_id"] = str(user["_id"])
        return UserInDB(**user)
    return None

async def authenticate_user(db, username: str, password: str):
    user = await get_user_by_username(db, username)
    if not user:
        return False
    if not verify_password(password, user.hashed_password):
        return False
    return user

async def get_current_user(token: str = Depends(oauth2_scheme), db = Depends(get_database)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    
    user = await get_user_by_username(db, username=token_data.username)
    if user is None:
        raise credentials_exception
    return user

@router.post("/test-token")
async def test_login(
    request: Request,
    db = Depends(get_database)
):
    """Test login endpoint for debugging"""
    try:
        body = await request.body()
        print(f"Request body: {body}")

        form = await request.form()
        print(f"Form data: {dict(form)}")

        username = form.get("username")
        password = form.get("password")

        if not username or not password:
            return {"error": "Missing username or password", "form": dict(form)}

        user = await authenticate_user(db, username, password)
        if user:
            return {"success": True, "user": user.username}
        else:
            return {"success": False, "message": "Invalid credentials"}
    except Exception as e:
        return {"error": str(e)}

@router.post("/token", response_model=Token)
async def login(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db = Depends(get_database)
):
    user = await authenticate_user(db, form_data.username, form_data.password)
    if not user:
        # Log failed login attempt - temporarily disabled for debugging
        # await log_audit_event(
        #     db, None, form_data.username, AuditAction.LOGIN,
        #     "authentication", None,
        #     {"success": False, "reason": "invalid_credentials"},
        #     request.client.host if request.client else None
        # )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Update last login
    await db.users.update_one(
        {"username": user.username},
        {"$set": {"last_login": datetime.utcnow(), "failed_login_attempts": 0}}
    )
    
    # Log successful login - temporarily disabled for debugging
    # await log_audit_event(
    #     db, str(user.id), user.username, AuditAction.LOGIN,
    #     "authentication", None,
    #     {"success": True},
    #     request.client.host if request.client else None
    # )
    
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username, "role": user.role},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "user": User(**user.dict())
    }

@router.post("/register", response_model=User, status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserCreate,
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Register a new user (Admin only)"""

    # Check if current user is admin
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can create new users"
        )

    # Validate password policy
    if not validate_password_policy(user_data.password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password does not meet security requirements"
        )

    # Check if user already exists
    existing_user = await db.users.find_one({
        "$or": [
            {"username": user_data.username},
            {"email": user_data.email}
        ]
    })

    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User with this username or email already exists"
        )

    # Create user
    user_dict = user_data.dict()
    user_dict.pop("password")
    user_dict.update({
        "_id": ObjectId(),
        "hashed_password": get_password_hash(user_data.password),
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
        "status": "active",
        "failed_login_attempts": 0,
        "password_history": [get_password_hash(user_data.password)]
    })

    result = await db.users.insert_one(user_dict)
    created_user = await db.users.find_one({"_id": result.inserted_id})

    return User(**created_user)

@router.get("/me", response_model=User)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """Get current user information"""
    return current_user

@router.put("/change-password")
async def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Change user password"""

    # Verify current password
    user_in_db = await get_user_by_username(db, current_user.username)
    if not verify_password(password_data.current_password, user_in_db.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )

    # Validate new password policy
    if not validate_password_policy(password_data.new_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="New password does not meet security requirements"
        )

    # Check password history
    new_password_hash = get_password_hash(password_data.new_password)
    for old_hash in user_in_db.password_history[-settings.PASSWORD_HISTORY_COUNT:]:
        if verify_password(password_data.new_password, old_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot reuse recent passwords"
            )

    # Update password
    updated_history = user_in_db.password_history + [new_password_hash]
    if len(updated_history) > settings.PASSWORD_HISTORY_COUNT:
        updated_history = updated_history[-settings.PASSWORD_HISTORY_COUNT:]

    await db.users.update_one(
        {"username": current_user.username},
        {
            "$set": {
                "hashed_password": new_password_hash,
                "password_changed_at": datetime.utcnow(),
                "password_history": updated_history,
                "updated_at": datetime.utcnow()
            }
        }
    )

    return {"message": "Password changed successfully"}
