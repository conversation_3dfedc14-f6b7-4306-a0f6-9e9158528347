from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from typing import List, Optional
from datetime import datetime
from app.models.wip import WIPTracking, WIPTrackingCreate, WIPTrackingUpdate, ProjectStatus
from app.models.user import User
from app.models.audit import AuditAction
from app.core.database import get_database
from app.routers.auth import get_current_user
from app.services.audit_service import log_audit_event
from bson import ObjectId

router = APIRouter()

def wip_helper(wip) -> dict:
    """Helper function to convert MongoDB document to dict"""
    if wip:
        wip["id"] = str(wip["_id"])
        return wip
    return None

@router.post("/", response_model=WIPTracking, status_code=status.HTTP_201_CREATED)
async def create_wip_record(
    wip_data: WIPTrackingCreate,
    request: Request,
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Create WIP tracking record"""

    # Check permissions
    if current_user.role not in ["admin", "asset_manager"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to create WIP records"
        )

    # Check if asset exists
    asset = await db.assets.find_one({"asset_id": wip_data.asset_id})
    if not asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )

    # Check if WIP record already exists for this asset
    existing_wip = await db.wip_tracking.find_one({"asset_id": wip_data.asset_id})
    if existing_wip:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="WIP record already exists for this asset"
        )

    # Create WIP record
    wip_dict = wip_data.dict()
    wip_dict.update({
        "_id": ObjectId(),
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
        "created_by": current_user.username,
        "updated_by": current_user.username
    })

    # Insert WIP record
    result = await db.wip_tracking.insert_one(wip_dict)

    # Log audit event
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.CREATE,
        "wip", wip_data.asset_id,
        {"wip_data": wip_data.dict()},
        request.client.host if request.client else None
    )

    # Retrieve and return the created record
    created_wip = await db.wip_tracking.find_one({"_id": result.inserted_id})
    return WIPTracking(**wip_helper(created_wip))

@router.get("/", response_model=List[WIPTracking])
async def get_wip_records(
    status: Optional[ProjectStatus] = Query(None, description="Filter by project status"),
    project_manager: Optional[str] = Query(None, description="Filter by project manager"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get WIP tracking records"""

    # Build filter query
    filter_query = {}

    if status:
        filter_query["project_status"] = status.value

    if project_manager:
        filter_query["project_manager"] = {"$regex": project_manager, "$options": "i"}

    # Get WIP records
    cursor = db.wip_tracking.find(filter_query).skip(skip).limit(limit).sort("created_at", -1)
    wip_records = await cursor.to_list(length=limit)

    return [WIPTracking(**wip_helper(wip)) for wip in wip_records]

@router.get("/{wip_id}", response_model=WIPTracking)
async def get_wip_record(
    wip_id: str,
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get a specific WIP record"""

    try:
        wip = await db.wip_tracking.find_one({"_id": ObjectId(wip_id)})
    except:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid WIP record ID"
        )

    if not wip:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="WIP record not found"
        )

    return WIPTracking(**wip_helper(wip))

@router.put("/{wip_id}", response_model=WIPTracking)
async def update_wip_record(
    wip_id: str,
    wip_update: WIPTrackingUpdate,
    request: Request,
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Update a WIP record"""

    # Check permissions
    if current_user.role not in ["admin", "asset_manager"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to update WIP records"
        )

    # Check if WIP record exists
    try:
        existing_wip = await db.wip_tracking.find_one({"_id": ObjectId(wip_id)})
    except:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid WIP record ID"
        )

    if not existing_wip:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="WIP record not found"
        )

    # Prepare update data
    update_data = {k: v for k, v in wip_update.dict().items() if v is not None}
    if update_data:
        update_data["updated_at"] = datetime.utcnow()
        update_data["updated_by"] = current_user.username

        # Log what changed for audit
        changes = {}
        for key, new_value in update_data.items():
            if key in existing_wip and existing_wip[key] != new_value:
                changes[key] = {
                    "old_value": existing_wip[key],
                    "new_value": new_value
                }

        # Update WIP record
        await db.wip_tracking.update_one(
            {"_id": ObjectId(wip_id)},
            {"$set": update_data}
        )

        # Log audit event
        await log_audit_event(
            db, str(current_user.id), current_user.username, AuditAction.UPDATE,
            "wip", existing_wip["asset_id"],
            {"changes": changes},
            request.client.host if request.client else None
        )

    # Return updated WIP record
    updated_wip = await db.wip_tracking.find_one({"_id": ObjectId(wip_id)})
    return WIPTracking(**wip_helper(updated_wip))

@router.delete("/{wip_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_wip_record(
    wip_id: str,
    request: Request,
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Delete a WIP record"""

    # Check permissions
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can delete WIP records"
        )

    # Check if WIP record exists
    try:
        existing_wip = await db.wip_tracking.find_one({"_id": ObjectId(wip_id)})
    except:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid WIP record ID"
        )

    if not existing_wip:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="WIP record not found"
        )

    # Delete WIP record
    await db.wip_tracking.delete_one({"_id": ObjectId(wip_id)})

    # Log audit event
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.DELETE,
        "wip", existing_wip["asset_id"],
        {"deleted_wip": existing_wip["project_name"]},
        request.client.host if request.client else None
    )

@router.get("/asset/{asset_id}", response_model=WIPTracking)
async def get_wip_by_asset(
    asset_id: str,
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get WIP record by asset ID"""

    wip = await db.wip_tracking.find_one({"asset_id": asset_id})
    if not wip:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No WIP record found for this asset"
        )

    return WIPTracking(**wip_helper(wip))
