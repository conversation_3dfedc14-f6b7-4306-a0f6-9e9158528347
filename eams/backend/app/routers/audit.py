from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional
from datetime import datetime
from app.models.audit import AuditTrail, AuditAction
from app.models.user import User
from app.core.database import get_database
from app.routers.auth import get_current_user
from app.services.audit_service import get_audit_trail

router = APIRouter()

def audit_helper(audit) -> dict:
    """Helper function to convert MongoDB document to dict"""
    if audit:
        audit["id"] = str(audit["_id"])
        return audit
    return None

@router.get("/", response_model=List[AuditTrail])
async def get_audit_trail_records(
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    username: Optional[str] = Query(None, description="Filter by username"),
    resource_type: Optional[str] = Query(None, description="Filter by resource type"),
    resource_id: Optional[str] = Query(None, description="Filter by resource ID"),
    action: Optional[AuditAction] = Query(None, description="Filter by action"),
    start_date: Optional[datetime] = Query(None, description="Filter from date"),
    end_date: Optional[datetime] = Query(None, description="Filter to date"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get audit trail records with filtering"""

    # Check permissions - only admin and auditors can view audit trails
    if current_user.role not in ["admin", "auditor"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to view audit trail"
        )

    # Build filter query
    filter_query = {}

    if user_id:
        filter_query["user_id"] = user_id

    if username:
        filter_query["username"] = {"$regex": username, "$options": "i"}

    if resource_type:
        filter_query["resource_type"] = resource_type

    if resource_id:
        filter_query["resource_id"] = resource_id

    if action:
        filter_query["action"] = action.value

    if start_date or end_date:
        date_filter = {}
        if start_date:
            date_filter["$gte"] = start_date
        if end_date:
            date_filter["$lte"] = end_date
        filter_query["timestamp"] = date_filter

    # Get audit records
    cursor = db.audit_trail.find(filter_query).skip(skip).limit(limit).sort("timestamp", -1)
    audit_records = await cursor.to_list(length=limit)

    return [AuditTrail(**audit_helper(record)) for record in audit_records]

@router.get("/statistics")
async def get_audit_statistics(
    start_date: Optional[datetime] = Query(None, description="Statistics from date"),
    end_date: Optional[datetime] = Query(None, description="Statistics to date"),
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get audit trail statistics"""

    # Check permissions
    if current_user.role not in ["admin", "auditor"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to view audit statistics"
        )

    # Build date filter
    date_filter = {}
    if start_date or end_date:
        if start_date:
            date_filter["$gte"] = start_date
        if end_date:
            date_filter["$lte"] = end_date

    match_stage = {"timestamp": date_filter} if date_filter else {}

    # Aggregate statistics by action
    action_pipeline = [
        {"$match": match_stage},
        {
            "$group": {
                "_id": "$action",
                "count": {"$sum": 1}
            }
        }
    ]

    # Aggregate statistics by user
    user_pipeline = [
        {"$match": match_stage},
        {
            "$group": {
                "_id": "$username",
                "count": {"$sum": 1}
            }
        },
        {"$sort": {"count": -1}},
        {"$limit": 10}
    ]

    # Aggregate statistics by resource type
    resource_pipeline = [
        {"$match": match_stage},
        {
            "$group": {
                "_id": "$resource_type",
                "count": {"$sum": 1}
            }
        }
    ]

    # Execute aggregations
    action_cursor = db.audit_trail.aggregate(action_pipeline)
    user_cursor = db.audit_trail.aggregate(user_pipeline)
    resource_cursor = db.audit_trail.aggregate(resource_pipeline)

    action_stats = await action_cursor.to_list(length=None)
    user_stats = await user_cursor.to_list(length=None)
    resource_stats = await resource_cursor.to_list(length=None)

    # Get total count
    total_count = await db.audit_trail.count_documents(match_stage)

    return {
        "total_events": total_count,
        "by_action": {item["_id"]: item["count"] for item in action_stats},
        "top_users": [{"username": item["_id"], "count": item["count"]} for item in user_stats],
        "by_resource_type": {item["_id"]: item["count"] for item in resource_stats},
        "period": {
            "start_date": start_date,
            "end_date": end_date
        }
    }

@router.get("/user/{user_id}")
async def get_user_audit_trail(
    user_id: str,
    start_date: Optional[datetime] = Query(None, description="Filter from date"),
    end_date: Optional[datetime] = Query(None, description="Filter to date"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get audit trail for a specific user"""

    # Check permissions
    if current_user.role not in ["admin", "auditor"]:
        # Users can only view their own audit trail
        if str(current_user.id) != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view this user's audit trail"
            )

    return await get_audit_trail(
        db, user_id=user_id, start_date=start_date, end_date=end_date,
        limit=limit, skip=skip
    )

@router.get("/recent")
async def get_recent_activities(
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get recent audit activities for dashboard"""

    # Get recent audit logs
    cursor = db.audit_trail.find({}).sort("timestamp", -1).limit(10)
    activities = await cursor.to_list(10)

    # Convert ObjectId to string and format for frontend
    formatted_activities = []
    for activity in activities:
        # Handle both enum and string values for action
        action_value = activity['action']
        if hasattr(action_value, 'value'):
            action_str = action_value.value
        else:
            action_str = str(action_value)

        formatted_activities.append({
            "_id": str(activity["_id"]),
            "action": f"{action_str} {activity['resource_type']}",
            "entity_type": activity["resource_type"],
            "entity_id": activity["resource_id"],
            "user_id": activity["user_id"],
            "user_name": activity["username"],
            "timestamp": activity["timestamp"].isoformat(),
            "details": activity.get("details", {})
        })

    return formatted_activities
