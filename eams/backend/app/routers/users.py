from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional
from datetime import datetime, timedelta
from app.models.user import (
    User, UserCreate, UserUpdate, UserInDB, UserRole, UserStatus,
    PasswordReset, UserStatusUpdate, RoleAssignment
)
from app.models.audit import AuditAction
from app.core.database import get_database
from app.routers.auth import get_current_user, get_password_hash, verify_password, validate_password_policy
from app.services.audit_service import log_audit_event
from bson import ObjectId
import secrets
import string

router = APIRouter()

def require_admin_role(current_user: User = Depends(get_current_user)):
    """Dependency to ensure only admin users can access user management endpoints"""
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin role required"
        )
    return current_user

def require_admin_or_auditor_role(current_user: User = Depends(get_current_user)):
    """Dependency to allow admin and auditor users read-only access"""
    if current_user.role not in [UserRole.ADMIN, UserRole.AUDITOR]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin or Auditor role required"
        )
    return current_user

def require_any_authenticated_user(current_user: User = Depends(get_current_user)):
    """Dependency to ensure user is authenticated (any role)"""
    return current_user

def user_helper(user) -> dict:
    """Helper function to convert MongoDB document to dict"""
    if user:
        # Convert ObjectId to string
        user["id"] = str(user["_id"])
        user.pop("_id", None)  # Remove the original _id field

        # Remove sensitive fields
        user.pop("hashed_password", None)
        user.pop("password_history", None)

        # Convert datetime fields to ISO format strings if they exist
        for field in ["created_at", "updated_at", "last_login", "password_changed_at", "locked_until"]:
            if field in user and user[field] is not None:
                user[field] = user[field].isoformat() if hasattr(user[field], 'isoformat') else user[field]

        return user
    return None

@router.get("/", response_model=List[User])
async def get_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    role: Optional[UserRole] = None,
    status: Optional[UserStatus] = None,
    search: Optional[str] = None,
    current_user: User = Depends(require_admin_or_auditor_role),
    db = Depends(get_database)
):
    """Get all users with optional filtering"""
    
    # Build query
    query = {}
    if role:
        query["role"] = role
    if status:
        query["status"] = status
    if search:
        query["$or"] = [
            {"username": {"$regex": search, "$options": "i"}},
            {"full_name": {"$regex": search, "$options": "i"}},
            {"email": {"$regex": search, "$options": "i"}},
            {"department": {"$regex": search, "$options": "i"}}
        ]
    
    # Get users
    cursor = db.users.find(query).skip(skip).limit(limit).sort("created_at", -1)
    users = await cursor.to_list(length=limit)
    
    return [user_helper(user) for user in users]

@router.get("/{user_id}", response_model=User)
async def get_user(
    user_id: str,
    current_user: User = Depends(require_admin_role),
    db = Depends(get_database)
):
    """Get a specific user by ID"""
    
    if not ObjectId.is_valid(user_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid user ID"
        )
    
    user = await db.users.find_one({"_id": ObjectId(user_id)})
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return user_helper(user)

@router.post("/", response_model=User, status_code=status.HTTP_201_CREATED)
async def create_user(
    user_data: UserCreate,
    current_user: User = Depends(require_admin_role),
    db = Depends(get_database)
):
    """Create a new user"""

    # Validate password policy
    if not validate_password_policy(user_data.password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password does not meet security requirements: minimum 12 characters with uppercase, lowercase, numbers, and special characters"
        )

    # Check if username or email already exists
    existing_user = await db.users.find_one({
        "$or": [
            {"username": user_data.username},
            {"email": user_data.email}
        ]
    })

    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username or email already exists"
        )
    
    # Hash password
    hashed_password = get_password_hash(user_data.password)
    
    # Create user document
    user_dict = user_data.dict()
    user_dict.pop("password")
    user_dict.update({
        "_id": ObjectId(),
        "hashed_password": hashed_password,
        "status": UserStatus.ACTIVE,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
        "password_changed_at": datetime.utcnow(),
        "failed_login_attempts": 0,
        "password_history": [hashed_password]
    })
    
    # Insert user
    result = await db.users.insert_one(user_dict)
    
    # Log the action
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.CREATE,
        "user", str(result.inserted_id),
        {"username": user_data.username, "role": user_data.role},
        None
    )
    
    # Get created user
    created_user = await db.users.find_one({"_id": result.inserted_id})
    return user_helper(created_user)

@router.put("/{user_id}", response_model=User)
async def update_user(
    user_id: str,
    user_data: UserUpdate,
    current_user: User = Depends(require_admin_role),
    db = Depends(get_database)
):
    """Update a user"""
    
    if not ObjectId.is_valid(user_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid user ID"
        )
    
    # Get existing user
    existing_user = await db.users.find_one({"_id": ObjectId(user_id)})
    if not existing_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Prevent admin from changing their own role or status
    if str(existing_user["_id"]) == str(current_user.id):
        if user_data.role and user_data.role != current_user.role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot change your own role"
            )
        if user_data.status and user_data.status != UserStatus.ACTIVE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot deactivate your own account"
            )
    
    # Check for email conflicts
    if user_data.email and user_data.email != existing_user.get("email"):
        email_conflict = await db.users.find_one({
            "email": user_data.email,
            "_id": {"$ne": ObjectId(user_id)}
        })
        if email_conflict:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already exists"
            )
    
    # Update user
    update_data = {k: v for k, v in user_data.dict().items() if v is not None}
    update_data["updated_at"] = datetime.utcnow()
    
    await db.users.update_one(
        {"_id": ObjectId(user_id)},
        {"$set": update_data}
    )
    
    # Log the action
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.UPDATE,
        "user", user_id,
        update_data,
        None
    )
    
    # Get updated user
    updated_user = await db.users.find_one({"_id": ObjectId(user_id)})
    return user_helper(updated_user)

def generate_random_password(length: int = 16) -> str:
    """Generate a secure random password"""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    password = ''.join(secrets.choice(alphabet) for _ in range(length))
    return password

@router.post("/{user_id}/reset-password")
async def reset_user_password(
    user_id: str,
    current_user: User = Depends(require_admin_role),
    db = Depends(get_database)
):
    """Reset a user's password to a random password"""

    if not ObjectId.is_valid(user_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid user ID"
        )

    # Get existing user
    existing_user = await db.users.find_one({"_id": ObjectId(user_id)})
    if not existing_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Generate new password
    new_password = generate_random_password()
    hashed_password = get_password_hash(new_password)

    # Update user password
    await db.users.update_one(
        {"_id": ObjectId(user_id)},
        {
            "$set": {
                "hashed_password": hashed_password,
                "password_changed_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                "failed_login_attempts": 0,
                "locked_until": None
            },
            "$push": {"password_history": hashed_password}
        }
    )

    # Log the action
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.UPDATE,
        "user", user_id,
        {"action": "password_reset"},
        None
    )

    return {
        "message": "Password reset successfully",
        "new_password": new_password,
        "user_id": user_id
    }

@router.post("/{user_id}/change-status")
async def change_user_status(
    user_id: str,
    status_data: UserStatusUpdate,
    current_user: User = Depends(require_admin_role),
    db = Depends(get_database)
):
    """Change a user's status (activate/deactivate/suspend)"""

    if not ObjectId.is_valid(user_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid user ID"
        )

    # Get existing user
    existing_user = await db.users.find_one({"_id": ObjectId(user_id)})
    if not existing_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Prevent admin from deactivating themselves
    if str(existing_user["_id"]) == str(current_user.id) and status_data.status != UserStatus.ACTIVE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot deactivate your own account"
        )

    # Update user status
    update_data = {
        "status": status_data.status,
        "updated_at": datetime.utcnow()
    }

    # Clear lockout if activating
    if status_data.status == UserStatus.ACTIVE:
        update_data.update({
            "failed_login_attempts": 0,
            "locked_until": None
        })

    await db.users.update_one(
        {"_id": ObjectId(user_id)},
        {"$set": update_data}
    )

    # Log the action
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.UPDATE,
        "user", user_id,
        {"status": status_data.status, "reason": status_data.reason},
        None
    )

    return {
        "message": f"User status changed to {status_data.status}",
        "user_id": user_id,
        "new_status": status_data.status
    }

@router.post("/{user_id}/assign-role")
async def assign_user_role(
    user_id: str,
    role_data: RoleAssignment,
    current_user: User = Depends(require_admin_role),
    db = Depends(get_database)
):
    """Assign a new role to a user"""

    if not ObjectId.is_valid(user_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid user ID"
        )

    # Get existing user
    existing_user = await db.users.find_one({"_id": ObjectId(user_id)})
    if not existing_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Prevent admin from changing their own role
    if str(existing_user["_id"]) == str(current_user.id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot change your own role"
        )

    # Update user role
    await db.users.update_one(
        {"_id": ObjectId(user_id)},
        {
            "$set": {
                "role": role_data.role,
                "updated_at": datetime.utcnow()
            }
        }
    )

    # Log the action
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.UPDATE,
        "user", user_id,
        {"role": role_data.role, "reason": role_data.reason},
        None
    )

    return {
        "message": f"User role changed to {role_data.role}",
        "user_id": user_id,
        "new_role": role_data.role
    }

@router.delete("/{user_id}")
async def delete_user(
    user_id: str,
    current_user: User = Depends(require_admin_role),
    db = Depends(get_database)
):
    """Delete a user (soft delete by setting status to inactive)"""

    if not ObjectId.is_valid(user_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid user ID"
        )

    # Get existing user
    existing_user = await db.users.find_one({"_id": ObjectId(user_id)})
    if not existing_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Prevent admin from deleting themselves
    if str(existing_user["_id"]) == str(current_user.id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete your own account"
        )

    # Soft delete by setting status to inactive
    await db.users.update_one(
        {"_id": ObjectId(user_id)},
        {
            "$set": {
                "status": UserStatus.INACTIVE,
                "updated_at": datetime.utcnow()
            }
        }
    )

    # Log the action
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.DELETE,
        "user", user_id,
        {"action": "soft_delete"},
        None
    )

    return {
        "message": "User deleted successfully",
        "user_id": user_id
    }

@router.get("/stats/summary")
async def get_user_stats(
    current_user: User = Depends(require_admin_or_auditor_role),
    db = Depends(get_database)
):
    """Get user statistics summary"""

    # Get user counts by status
    pipeline = [
        {"$group": {"_id": "$status", "count": {"$sum": 1}}}
    ]
    status_counts = await db.users.aggregate(pipeline).to_list(length=None)

    # Get user counts by role
    pipeline = [
        {"$group": {"_id": "$role", "count": {"$sum": 1}}}
    ]
    role_counts = await db.users.aggregate(pipeline).to_list(length=None)

    # Get total users
    total_users = await db.users.count_documents({})

    # Get active users
    active_users = await db.users.count_documents({"status": UserStatus.ACTIVE})

    # Get recent logins (last 30 days)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    recent_logins = await db.users.count_documents({
        "last_login": {"$gte": thirty_days_ago}
    })

    return {
        "total_users": total_users,
        "active_users": active_users,
        "recent_logins": recent_logins,
        "status_breakdown": {item["_id"]: item["count"] for item in status_counts},
        "role_breakdown": {item["_id"]: item["count"] for item in role_counts}
    }
