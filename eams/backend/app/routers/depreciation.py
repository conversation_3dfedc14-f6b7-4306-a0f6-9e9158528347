from fastapi import APIRouter, Depends, HTTPException, status
from typing import List
from datetime import datetime, timedelta
from app.models.user import User
from app.models.wip import DepreciationCalculation
from app.models.asset import Asset
from app.core.database import get_database
from app.routers.auth import get_current_user
from app.services.audit_service import log_audit_event
from app.models.audit import AuditAction
from bson import ObjectId

router = APIRouter()

def asset_helper(asset) -> dict:
    """Helper function to convert MongoDB document to dict"""
    if asset:
        asset["id"] = str(asset["_id"])
        return asset
    return None

def calculate_straight_line_depreciation(
    purchase_cost: float,
    useful_life_years: int,
    purchase_date: datetime,
    calculation_date: datetime = None
) -> dict:
    """Calculate straight-line depreciation"""

    if calculation_date is None:
        calculation_date = datetime.utcnow()

    # Calculate years elapsed
    years_elapsed = (calculation_date - purchase_date).days / 365.25

    # Annual depreciation
    annual_depreciation = purchase_cost / useful_life_years

    # Accumulated depreciation (capped at purchase cost)
    accumulated_depreciation = min(annual_depreciation * years_elapsed, purchase_cost)

    # Current value
    current_value = max(purchase_cost - accumulated_depreciation, 0)

    return {
        "annual_depreciation": round(annual_depreciation, 2),
        "accumulated_depreciation": round(accumulated_depreciation, 2),
        "current_value": round(current_value, 2),
        "years_elapsed": round(years_elapsed, 2)
    }

@router.post("/calculate/{asset_id}")
async def calculate_depreciation(
    asset_id: str,
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Calculate depreciation for an asset"""

    # Get asset
    asset = await db.assets.find_one({"asset_id": asset_id})
    if not asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )

    # Calculate depreciation
    calculation = calculate_straight_line_depreciation(
        asset["purchase_cost"],
        asset["useful_life_years"],
        asset["purchase_date"]
    )

    # Update asset with new values
    await db.assets.update_one(
        {"asset_id": asset_id},
        {
            "$set": {
                "current_value": calculation["current_value"],
                "accumulated_depreciation": calculation["accumulated_depreciation"],
                "updated_at": datetime.utcnow(),
                "updated_by": current_user.username
            }
        }
    )

    # Save calculation record
    calc_record = {
        "_id": ObjectId(),
        "asset_id": asset_id,
        "calculation_date": datetime.utcnow(),
        "purchase_cost": asset["purchase_cost"],
        "useful_life_years": asset["useful_life_years"],
        "annual_depreciation": calculation["annual_depreciation"],
        "accumulated_depreciation": calculation["accumulated_depreciation"],
        "current_value": calculation["current_value"],
        "depreciation_method": "straight_line",
        "calculated_by": current_user.username
    }

    await db.depreciation_calculations.insert_one(calc_record)

    # Log audit event
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.DEPRECIATION_CALC,
        "asset", asset_id,
        calculation
    )

    return {
        "asset_id": asset_id,
        "calculation_date": datetime.utcnow(),
        **calculation
    }

@router.get("/summary")
async def get_depreciation_summary(
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get depreciation summary"""

    # Aggregate depreciation data
    pipeline = [
        {
            "$group": {
                "_id": "$depreciation_class",
                "total_purchase_cost": {"$sum": "$purchase_cost"},
                "total_current_value": {"$sum": "$current_value"},
                "total_accumulated_depreciation": {"$sum": "$accumulated_depreciation"},
                "asset_count": {"$sum": 1}
            }
        }
    ]

    cursor = db.assets.aggregate(pipeline)
    summary_by_class = await cursor.to_list(length=None)

    # Calculate totals
    total_purchase_cost = sum(item["total_purchase_cost"] for item in summary_by_class)
    total_current_value = sum(item["total_current_value"] for item in summary_by_class)
    total_accumulated_depreciation = sum(item["total_accumulated_depreciation"] for item in summary_by_class)
    total_assets = sum(item["asset_count"] for item in summary_by_class)

    return {
        "summary_by_class": summary_by_class,
        "totals": {
            "total_purchase_cost": round(total_purchase_cost, 2),
            "total_current_value": round(total_current_value, 2),
            "total_accumulated_depreciation": round(total_accumulated_depreciation, 2),
            "total_assets": total_assets,
            "depreciation_percentage": round((total_accumulated_depreciation / total_purchase_cost * 100) if total_purchase_cost > 0 else 0, 2)
        }
    }

@router.post("/calculate-all")
async def calculate_all_depreciation(
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Calculate depreciation for all assets"""

    # Check permissions
    if current_user.role not in ["admin", "asset_manager"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to calculate depreciation for all assets"
        )

    # Get all assets
    cursor = db.assets.find({})
    assets = await cursor.to_list(length=None)

    updated_count = 0

    for asset in assets:
        try:
            # Calculate depreciation
            calculation = calculate_straight_line_depreciation(
                asset["purchase_cost"],
                asset["useful_life_years"],
                asset["purchase_date"]
            )

            # Update asset
            await db.assets.update_one(
                {"_id": asset["_id"]},
                {
                    "$set": {
                        "current_value": calculation["current_value"],
                        "accumulated_depreciation": calculation["accumulated_depreciation"],
                        "updated_at": datetime.utcnow(),
                        "updated_by": current_user.username
                    }
                }
            )

            updated_count += 1

        except Exception as e:
            print(f"Error calculating depreciation for asset {asset.get('asset_id', 'unknown')}: {e}")

    # Log audit event
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.DEPRECIATION_CALC,
        "system", None,
        {"assets_updated": updated_count, "total_assets": len(assets)}
    )

    return {
        "message": f"Depreciation calculated for {updated_count} assets",
        "updated_count": updated_count,
        "total_assets": len(assets)
    }
