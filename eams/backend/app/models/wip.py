from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime
from enum import Enum

class ProjectStatus(str, Enum):
    PLANNING = "planning"
    IN_PROGRESS = "in_progress"
    ON_HOLD = "on_hold"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    READY_FOR_CAPITALIZATION = "ready_for_capitalization"
    CAPITALIZED = "capitalized"

class WIPTrackingBase(BaseModel):
    asset_id: str = Field(..., description="Asset identifier")
    project_name: str = Field(..., description="Project name")
    project_description: Optional[str] = Field(None, description="Project description")
    project_status: ProjectStatus = Field(..., description="Current project status")
    expected_capitalization_date: Optional[datetime] = Field(None, description="Expected date for capitalization")
    actual_capitalization_date: Optional[datetime] = Field(None, description="Actual capitalization date")
    project_manager: str = Field(..., description="Project manager name")
    estimated_cost: Optional[float] = Field(None, ge=0, description="Estimated project cost")
    actual_cost: Optional[float] = Field(None, ge=0, description="Actual project cost")
    notes: Optional[str] = Field(None, description="Additional notes")

class WIPTrackingCreate(WIPTrackingBase):
    pass

class WIPTrackingUpdate(BaseModel):
    project_name: Optional[str] = None
    project_description: Optional[str] = None
    project_status: Optional[ProjectStatus] = None
    expected_capitalization_date: Optional[datetime] = None
    actual_capitalization_date: Optional[datetime] = None
    project_manager: Optional[str] = None
    estimated_cost: Optional[float] = None
    actual_cost: Optional[float] = None
    notes: Optional[str] = None

class WIPTracking(WIPTrackingBase):
    id: Optional[str] = Field(None, alias="_id")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: str = Field(..., description="User who created the WIP record")
    updated_by: str = Field(..., description="User who last updated the WIP record")
    
    class Config:
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "asset_id": "AST-001",
                "project_name": "Office Renovation Phase 1",
                "project_description": "Renovation of office space including new furniture and equipment",
                "project_status": "in_progress",
                "expected_capitalization_date": "2024-03-31T00:00:00Z",
                "project_manager": "Jane Smith",
                "estimated_cost": 50000.00
            }
        }

class DepreciationCalculation(BaseModel):
    id: Optional[str] = Field(None, alias="_id")
    asset_id: str = Field(..., description="Asset identifier")
    calculation_date: datetime = Field(default_factory=datetime.utcnow)
    purchase_cost: float = Field(..., ge=0, description="Original purchase cost")
    useful_life_years: int = Field(..., ge=1, description="Useful life in years")
    annual_depreciation: float = Field(..., ge=0, description="Annual depreciation amount")
    accumulated_depreciation: float = Field(..., ge=0, description="Total depreciation to date")
    current_value: float = Field(..., ge=0, description="Current book value")
    depreciation_method: str = Field(default="straight_line", description="Depreciation method used")
    calculated_by: str = Field(..., description="User who triggered the calculation")
    
    class Config:
        populate_by_name = True
