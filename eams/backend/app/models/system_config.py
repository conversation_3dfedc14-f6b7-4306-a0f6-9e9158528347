from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum

class ModuleStatus(str, Enum):
    ENABLED = "enabled"
    DISABLED = "disabled"
    MAINTENANCE = "maintenance"

class ConfigCategory(str, Enum):
    MODULES = "modules"
    SECURITY = "security"
    NOTIFICATIONS = "notifications"
    SYSTEM = "system"
    AUDIT = "audit"

class SystemModule(BaseModel):
    name: str = Field(..., description="Module name")
    display_name: str = Field(..., description="Display name for the module")
    description: str = Field(..., description="Module description")
    status: ModuleStatus = Field(default=ModuleStatus.ENABLED, description="Module status")
    version: Optional[str] = Field(None, description="Module version")
    dependencies: List[str] = Field(default=[], description="Module dependencies")
    settings: Dict[str, Any] = Field(default={}, description="Module-specific settings")

class ConfigurationItem(BaseModel):
    key: str = Field(..., description="Configuration key")
    value: Any = Field(..., description="Configuration value")
    category: ConfigCategory = Field(..., description="Configuration category")
    description: str = Field(..., description="Configuration description")
    data_type: str = Field(..., description="Data type (string, boolean, integer, etc.)")
    is_sensitive: bool = Field(default=False, description="Whether this is sensitive data")
    requires_restart: bool = Field(default=False, description="Whether changing this requires system restart")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="Validation rules for the value")

class ConfigurationUpdate(BaseModel):
    value: Any = Field(..., description="New configuration value")
    reason: Optional[str] = Field(None, description="Reason for the change")

class SystemConfiguration(BaseModel):
    id: Optional[str] = Field(None, alias="_id")
    modules: Dict[str, SystemModule] = Field(default={}, description="System modules configuration")
    settings: Dict[str, ConfigurationItem] = Field(default={}, description="System settings")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    updated_by: str = Field(..., description="User who last updated the configuration")
    version: str = Field(default="1.0.0", description="Configuration version")
    
    class Config:
        populate_by_name = True

class ConfigurationHistory(BaseModel):
    id: Optional[str] = Field(None, alias="_id")
    config_key: str = Field(..., description="Configuration key that was changed")
    old_value: Any = Field(..., description="Previous value")
    new_value: Any = Field(..., description="New value")
    changed_by: str = Field(..., description="User who made the change")
    changed_at: datetime = Field(default_factory=datetime.utcnow)
    reason: Optional[str] = Field(None, description="Reason for the change")
    category: ConfigCategory = Field(..., description="Configuration category")

# Default system modules configuration
DEFAULT_MODULES = {
    "asset_registry": SystemModule(
        name="asset_registry",
        display_name="Asset Registry",
        description="Core asset management functionality",
        status=ModuleStatus.ENABLED,
        version="1.0.0",
        dependencies=[],
        settings={}
    ),
    "verification": SystemModule(
        name="verification",
        display_name="Asset Verification",
        description="Asset verification and audit functionality",
        status=ModuleStatus.ENABLED,
        version="1.0.0",
        dependencies=["asset_registry"],
        settings={}
    ),
    "wip_tracker": SystemModule(
        name="wip_tracker",
        display_name="WIP Tracker",
        description="Work in Progress tracking module",
        status=ModuleStatus.ENABLED,
        version="1.0.0",
        dependencies=["asset_registry"],
        settings={}
    ),
    "reports": SystemModule(
        name="reports",
        display_name="Reports & Analytics",
        description="Reporting and analytics functionality",
        status=ModuleStatus.ENABLED,
        version="1.0.0",
        dependencies=["asset_registry"],
        settings={}
    ),
    "audit_trail": SystemModule(
        name="audit_trail",
        display_name="Audit Trail",
        description="System audit and logging functionality",
        status=ModuleStatus.ENABLED,
        version="1.0.0",
        dependencies=[],
        settings={}
    ),
    "user_management": SystemModule(
        name="user_management",
        display_name="User Management",
        description="User and role management functionality",
        status=ModuleStatus.ENABLED,
        version="1.0.0",
        dependencies=[],
        settings={}
    )
}

# Default system settings
DEFAULT_SETTINGS = {
    "session_timeout": ConfigurationItem(
        key="session_timeout",
        value=3600,
        category=ConfigCategory.SECURITY,
        description="Session timeout in seconds",
        data_type="integer",
        validation_rules={"min": 300, "max": 86400}
    ),
    "password_policy_enabled": ConfigurationItem(
        key="password_policy_enabled",
        value=True,
        category=ConfigCategory.SECURITY,
        description="Enable password policy enforcement",
        data_type="boolean"
    ),
    "audit_retention_days": ConfigurationItem(
        key="audit_retention_days",
        value=365,
        category=ConfigCategory.AUDIT,
        description="Number of days to retain audit logs",
        data_type="integer",
        validation_rules={"min": 30, "max": 2555}
    ),
    "email_notifications": ConfigurationItem(
        key="email_notifications",
        value=True,
        category=ConfigCategory.NOTIFICATIONS,
        description="Enable email notifications",
        data_type="boolean"
    ),
    "max_failed_login_attempts": ConfigurationItem(
        key="max_failed_login_attempts",
        value=5,
        category=ConfigCategory.SECURITY,
        description="Maximum failed login attempts before account lockout",
        data_type="integer",
        validation_rules={"min": 3, "max": 10}
    )
}
