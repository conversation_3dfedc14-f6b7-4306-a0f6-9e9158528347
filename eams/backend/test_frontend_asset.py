#!/usr/bin/env python3
"""
Test asset creation exactly as the frontend would do it
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_frontend_asset_creation():
    print("🧪 Testing Frontend-Style Asset Creation")
    print("=" * 50)
    
    # Login first
    print("1. Logging in...")
    auth_data = {
        "username": "admin",
        "password": "admin123!@#"
    }
    response = requests.post(
        f"{BASE_URL}/api/auth/token",
        data=auth_data,
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    
    if response.status_code != 200:
        print(f"❌ Login failed: {response.json()}")
        return
    
    token = response.json()["access_token"]
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    print("✅ Login successful")
    
    # Test asset creation with frontend data structure
    print("\n2. Creating asset with frontend data structure...")
    
    # This mimics exactly what the frontend sends
    frontend_asset_data = {
        "asset_id": "FRONTEND-TEST-001",
        "description": "Test Laptop from Frontend",
        "location": "Building A - Floor 1",
        "custodian": "John Doe",
        "barcode": f"BC{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "purchase_date": datetime.now().isoformat(),
        "depreciation_class": "equipment",
        "purchase_cost": 1200.0,
        "useful_life_years": 5,
        "status": "active",
        "category": "Laptop",
        "notes": "Created via frontend test"
    }
    
    print(f"Frontend asset data:")
    print(json.dumps(frontend_asset_data, indent=2, default=str))
    
    response = requests.post(f"{BASE_URL}/api/assets/", headers=headers, json=frontend_asset_data)
    print(f"\nStatus: {response.status_code}")
    
    if response.status_code == 201:
        created_asset = response.json()
        print(f"✅ Asset created successfully!")
        print(f"Asset ID: {created_asset.get('asset_id', 'N/A')}")
        print(f"Description: {created_asset.get('description', 'N/A')}")
        return created_asset.get('asset_id')
    else:
        print(f"❌ Asset creation failed:")
        try:
            error_data = response.json()
            print(json.dumps(error_data, indent=2))
        except:
            print(response.text)
        return None

def test_asset_retrieval():
    print("\n3. Testing asset retrieval...")
    
    # Login
    auth_data = {
        "username": "admin",
        "password": "admin123!@#"
    }
    response = requests.post(
        f"{BASE_URL}/api/auth/token",
        data=auth_data,
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    
    token = response.json()["access_token"]
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Get assets
    response = requests.get(f"{BASE_URL}/api/assets/", headers=headers)
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        assets = response.json()
        print(f"✅ Retrieved {len(assets)} assets")
        if assets:
            print("Sample asset structure:")
            sample_asset = assets[0]
            print(json.dumps(sample_asset, indent=2, default=str))
    else:
        print(f"❌ Asset retrieval failed: {response.json()}")

if __name__ == "__main__":
    asset_id = test_frontend_asset_creation()
    test_asset_retrieval()
    
    print("\n" + "=" * 50)
    print("🎉 Frontend asset testing completed!")
    if asset_id:
        print(f"✅ Successfully created asset: {asset_id}")
    else:
        print("❌ Asset creation failed - check the errors above")
