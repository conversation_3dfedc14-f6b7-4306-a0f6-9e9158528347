# MongoDB Atlas Configuration
MONGODB_URL=mongodb+srv://username:<EMAIL>/asset_registry?retryWrites=true&w=majority
DATABASE_NAME=asset_registry

# JWT Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration
ALLOWED_HOSTS=["http://localhost:3000", "http://127.0.0.1:3000"]

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760

# ISO 27001 Password Policy
MIN_PASSWORD_LENGTH=12
REQUIRE_UPPERCASE=true
REQUIRE_LOWERCASE=true
REQUIRE_NUMBERS=true
REQUIRE_SPECIAL_CHARS=true
PASSWORD_HISTORY_COUNT=12
PASSWORD_EXPIRY_DAYS=90
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=30
