#!/usr/bin/env python3
"""
Simple API test script to verify the backend is working
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test the health check endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✓ Health check passed")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API server. Make sure it's running on http://localhost:8000")
        return False

def test_login():
    """Test login with default admin credentials"""
    try:
        data = {
            "username": "admin",
            "password": "admin123!@#"
        }
        response = requests.post(f"{BASE_URL}/api/auth/token", data=data)
        
        if response.status_code == 200:
            result = response.json()
            print("✓ Login successful")
            print(f"  Token: {result['access_token'][:20]}...")
            print(f"  User: {result['user']['full_name']} ({result['user']['role']})")
            return result['access_token']
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"  Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_assets_endpoint(token):
    """Test the assets endpoint"""
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BASE_URL}/api/assets/", headers=headers)
        
        if response.status_code == 200:
            assets = response.json()
            print(f"✓ Assets endpoint working (found {len(assets)} assets)")
            return True
        else:
            print(f"❌ Assets endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Assets endpoint error: {e}")
        return False

def main():
    print("🧪 Testing Asset Registry API...")
    print("=" * 40)
    
    # Test health check
    if not test_health_check():
        return
    
    # Test login
    token = test_login()
    if not token:
        print("\n💡 Make sure you've run the database initialization script:")
        print("   python init_db.py")
        return
    
    # Test protected endpoint
    test_assets_endpoint(token)
    
    print("\n🎉 All tests passed! The API is working correctly.")
    print("\nYou can now:")
    print("1. Open http://localhost:8000/docs to see the API documentation")
    print("2. Start the React frontend and login with admin/admin123!@#")

if __name__ == "__main__":
    main()
