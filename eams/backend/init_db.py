#!/usr/bin/env python3
"""
Database initialization script for Asset Registry
Creates default admin user and sets up initial data
"""

import asyncio
import sys
from datetime import datetime
from motor.motor_asyncio import AsyncIOMotorClient
from passlib.context import CryptContext
from app.core.config import settings

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

async def init_database():
    """Initialize database with default admin user"""
    
    print("Connecting to MongoDB...")
    client = AsyncIOMotorClient(settings.MONGODB_URL)
    db = client[settings.DATABASE_NAME]
    
    try:
        # Test connection
        await client.admin.command('ping')
        print("✓ Connected to MongoDB successfully")
        
        # Check if admin user already exists
        existing_admin = await db.users.find_one({"username": "admin"})
        if existing_admin:
            print("✓ Admin user already exists")
            return
        
        # Create default admin user
        admin_user = {
            "username": "admin",
            "email": "<EMAIL>",
            "full_name": "System Administrator",
            "role": "admin",
            "department": "IT",
            "phone": "+1234567890",
            "status": "active",
            "hashed_password": pwd_context.hash("AdminPass123!@#"),
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "last_login": None,
            "password_changed_at": datetime.utcnow(),
            "failed_login_attempts": 0,
            "locked_until": None,
            "password_history": []
        }
        
        await db.users.insert_one(admin_user)
        print("✓ Created default admin user")
        print("  Username: admin")
        print("  Password: AdminPass123!@#")
        print("  ⚠️  Please change the default password after first login!")
        
        # Create indexes
        print("Creating database indexes...")
        
        # Users collection indexes
        await db.users.create_index("email", unique=True)
        await db.users.create_index("username", unique=True)
        
        # Assets collection indexes
        await db.assets.create_index("asset_id", unique=True)
        await db.assets.create_index("barcode", unique=True, sparse=True)
        await db.assets.create_index("location")
        await db.assets.create_index("custodian")
        await db.assets.create_index("depreciation_class")
        
        # Verification events indexes
        await db.verification_events.create_index("asset_id")
        await db.verification_events.create_index("verification_date")
        await db.verification_events.create_index("verifier_id")
        
        # Audit trail indexes
        await db.audit_trail.create_index("asset_id")
        await db.audit_trail.create_index("user_id")
        await db.audit_trail.create_index("timestamp")
        await db.audit_trail.create_index("action")
        
        # WIP tracking indexes
        await db.wip_tracking.create_index("asset_id")
        await db.wip_tracking.create_index("project_status")
        
        print("✓ Database indexes created successfully")
        
        # Create sample depreciation classes
        depreciation_classes = [
            {"name": "building", "useful_life_years": 40, "description": "Buildings and structures"},
            {"name": "furniture", "useful_life_years": 10, "description": "Office furniture and fixtures"},
            {"name": "equipment", "useful_life_years": 15, "description": "General equipment"},
            {"name": "vehicle", "useful_life_years": 8, "description": "Vehicles and transportation"},
            {"name": "it_equipment", "useful_life_years": 3, "description": "IT equipment and computers"},
            {"name": "other", "useful_life_years": 5, "description": "Other assets"}
        ]
        
        for dep_class in depreciation_classes:
            existing = await db.depreciation_classes.find_one({"name": dep_class["name"]})
            if not existing:
                await db.depreciation_classes.insert_one(dep_class)
        
        print("✓ Sample depreciation classes created")
        
        print("\n🎉 Database initialization completed successfully!")
        print("\nNext steps:")
        print("1. Start the FastAPI server: uvicorn main:app --reload")
        print("2. Start the React frontend: npm start")
        print("3. Login with admin/AdminPass123!@# and change the password")
        
    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        sys.exit(1)
    finally:
        client.close()

if __name__ == "__main__":
    asyncio.run(init_database())
